package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// 示例数据结构
type ExampleData struct {
	Name    string   `json:"name"`
	Age     int      `json:"age"`
	Hobbies []string `json:"hobbies"`
	Address struct {
		Street string `json:"street"`
		City   string `json:"city"`
	} `json:"address"`
}

func demonstrateJSONBeautification() {
	// 示例数据
	data := ExampleData{
		Name:    "张三",
		Age:     30,
		Hobbies: []string{"阅读", "游泳", "编程"},
	}
	data.Address.Street = "中山路123号"
	data.Address.City = "北京"

	fmt.Println("JSON美化示例")
	fmt.Println("============")

	// 方法1：使用2个空格缩进
	fmt.Println("1. 使用2个空格缩进:")
	beautified1, _ := json.MarshalIndent(data, "", "  ")
	fmt.Println(string(beautified1))
	fmt.Println()

	// 方法2：使用4个空格缩进
	fmt.Println("2. 使用4个空格缩进:")
	beautified2, _ := json.MarshalIndent(data, "", "    ")
	fmt.Println(string(beautified2))
	fmt.Println()

	// 方法3：使用Tab缩进
	fmt.Println("3. 使用Tab缩进:")
	beautified3, _ := json.MarshalIndent(data, "", "\t")
	fmt.Println(string(beautified3))
	fmt.Println()

	// 方法4：带前缀的缩进
	fmt.Println("4. 带前缀的缩进:")
	beautified4, _ := json.MarshalIndent(data, ">>> ", "  ")
	fmt.Println(string(beautified4))
	fmt.Println()

	// 保存到文件的完整示例
	saveBeautifiedJSON(data)
}

// 保存美化JSON到文件的完整示例
func saveBeautifiedJSON(data interface{}) {
	// 方法1：直接使用 MarshalIndent + WriteFile
	beautified, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}

	err = os.WriteFile("example_beautified.json", beautified, 0644)
	if err != nil {
		fmt.Printf("写入文件失败: %v\n", err)
		return
	}

	// 方法2：使用 Encoder 直接写入文件（推荐用于大文件）
	file, err := os.Create("example_encoder.json")
	if err != nil {
		fmt.Printf("创建文件失败: %v\n", err)
		return
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ") // 设置缩进
	encoder.SetEscapeHTML(false) // 不转义HTML字符
	
	err = encoder.Encode(data)
	if err != nil {
		fmt.Printf("编码失败: %v\n", err)
		return
	}

	fmt.Println("✅ JSON文件已保存:")
	fmt.Println("   - example_beautified.json (使用 MarshalIndent)")
	fmt.Println("   - example_encoder.json (使用 Encoder)")
}

// 你的原始代码的改进版本
func saveInvestorDataBeautified(result interface{}) error {
	// 方法1：简单版本
	beautified, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}

	err = os.WriteFile("investor.json", beautified, 0644)
	if err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	fmt.Println("✅ investor.json 已保存（美化格式）")
	return nil
}

// 高级版本：带错误处理和自定义选项
func saveInvestorDataAdvanced(result interface{}, filename string, indent string) error {
	// 创建文件
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 创建JSON编码器
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", indent)        // 设置缩进
	encoder.SetEscapeHTML(false)         // 不转义HTML字符（如 &、<、>）
	
	// 编码并写入
	err = encoder.Encode(result)
	if err != nil {
		return fmt.Errorf("JSON编码失败: %v", err)
	}

	fmt.Printf("✅ %s 已保存（美化格式，缩进: %q）\n", filename, indent)
	return nil
}

// 使用示例
func exampleUsage() {
	// 假设你有一些数据
	var result interface{} = map[string]interface{}{
		"investors": []map[string]interface{}{
			{
				"name":     "红杉资本",
				"type":     "VC",
				"location": "北京",
				"cases":    []string{"字节跳动", "美团", "滴滴"},
			},
			{
				"name":     "IDG资本",
				"type":     "VC", 
				"location": "上海",
				"cases":    []string{"百度", "腾讯", "小米"},
			},
		},
		"total": 2,
	}

	// 使用不同的方法保存
	fmt.Println("保存示例:")
	
	// 简单版本
	err := saveInvestorDataBeautified(result)
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
	}

	// 高级版本 - 2个空格缩进
	err = saveInvestorDataAdvanced(result, "investor_2spaces.json", "  ")
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
	}

	// 高级版本 - Tab缩进
	err = saveInvestorDataAdvanced(result, "investor_tabs.json", "\t")
	if err != nil {
		fmt.Printf("保存失败: %v\n", err)
	}
}
