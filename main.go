package main

import (
	"fmt"
	"log"
	"time"
)

func main() {
	fmt.Println("36kr机构数据爬虫")
	fmt.Println("================")
	fmt.Println("1. 使用浏览器自动化爬取（复杂版本）")
	fmt.Println("2. 使用简单API爬取")
	fmt.Println("3. 使用直接浏览器爬取（推荐）")
	fmt.Print("请选择爬取方式 (1/2/3): ")

	var choice string
	fmt.Scanln(&choice)

	switch choice {
	case "1":
		runBrowserCrawler()
	case "2":
		runSimpleCrawler()
	case "3":
		runDirectCrawler()
	default:
		fmt.Println("无效选择，使用默认的直接浏览器爬取")
		runDirectCrawler()
	}
}

func runBrowserCrawler() {
	fmt.Println("\n使用浏览器自动化爬取...")

	// 创建爬虫实例
	crawler := NewCrawler()
	defer crawler.Close()

	// 开始爬取
	err := crawler.Start()
	if err != nil {
		log.Fatalf("爬取失败: %v", err)
	}

	// 保存结果
	filename := fmt.Sprintf("36kr_organizations_%s.json", time.Now().Format("20060102_150405"))
	err = crawler.SaveToFile(filename)
	if err != nil {
		log.Fatalf("保存文件失败: %v", err)
	}

	// 输出统计信息
	result := crawler.GetResult()
	fmt.Printf("\n=== 爬取完成 ===\n")
	fmt.Printf("总机构数量: %d\n", result.TotalCount)
	fmt.Printf("总页数: %d\n", result.TotalPages)
	fmt.Printf("结果已保存到: %s\n", filename)

	showSampleData(result.Organizations)
}

func runSimpleCrawler() {
	fmt.Println("\n使用简单API爬取...")

	// 创建简单爬虫实例
	crawler := NewSimpleCrawler()
	defer crawler.Close()

	// 开始爬取
	err := crawler.CrawlWithBrowser()
	if err != nil {
		log.Fatalf("爬取失败: %v", err)
	}

	fmt.Println("程序执行完成！")
}

func runDirectCrawler() {
	fmt.Println("\n使用直接浏览器爬取...")

	// 创建直接爬虫实例
	crawler := NewDirectCrawler()
	defer crawler.Close()

	// 开始爬取
	err := crawler.CrawlDirectly()
	if err != nil {
		log.Fatalf("爬取失败: %v", err)
	}

	fmt.Println("程序执行完成！")
}

func showSampleData(organizations []Organization) {
	// 显示前几个机构作为示例
	fmt.Printf("\n=== 示例数据 ===\n")
	for i, org := range organizations {
		if i >= 5 { // 只显示前5个
			break
		}
		fmt.Printf("%d. %s\n", i+1, org.Name)
		fmt.Printf("   类型: %s\n", org.Type)
		fmt.Printf("   地区: %s\n", org.Location)
		fmt.Printf("   投资案例: %d\n", org.CaseCount)
		fmt.Printf("   投资领域: %v\n", org.InvestArea)
		fmt.Printf("   链接: %s\n\n", org.URL)
	}
}
