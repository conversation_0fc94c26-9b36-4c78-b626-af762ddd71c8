package main

import (
	"fmt"
	"log"
	"time"
)

func main() {
	fmt.Println("36kr机构数据爬虫")
	fmt.Println("================")
	fmt.Println("1. 使用浏览器自动化爬取（复杂版本）")
	fmt.Println("2. 使用简单API爬取")
	fmt.Println("3. 使用直接浏览器爬取（推荐）")
	fmt.Print("请选择爬取方式 (1/2/3): ")

	var choice string
	fmt.Scanln(&choice)

	switch choice {
	case "1":
		runBrowserCrawler()
	case "2":
		runSimpleCrawler()
	case "3":
		runDirectCrawler()
	default:
		fmt.Println("无效选择，使用默认的直接浏览器爬取")
		runDirectCrawler()
	}
}
